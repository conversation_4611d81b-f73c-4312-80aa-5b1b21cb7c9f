<template>
  <div class="app-container room-management">
    <!-- 页面标题区域 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <el-icon><OfficeBuilding /></el-icon>
          会议室查询系统
        </h1>
        <p class="page-description">查询所有会议室信息，查看会议室状态和详情</p>
      </div>
    </div>

    <!-- 搜索表单区域 -->
    <div class="search-section" v-show="showSearch">
      <el-card class="search-card">
        <template #header>
          <div class="search-header">
            <span><el-icon><Search /></el-icon> 筛选条件</span>
          </div>
        </template>
        <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="80px" class="search-form">
          <el-form-item label="会议室" prop="roomId">
            <el-select
              v-model="queryParams.roomId"
              placeholder="请选择会议室"
              clearable
              style="width: 200px"
            >
              <el-option
                v-for="room in roomOptions"
                :key="room.roomId"
                :label="room.roomName"
                :value="room.roomId"
              />
            </el-select>
          </el-form-item>
          <!-- <el-form-item label="容纳人数" prop="capacity">
            <el-input-number
              v-model="queryParams.capacity"
              placeholder="最少容纳人数"
              :min="1"
              :max="1000"
              controls-position="right"
              style="width: 200px"
              @keyup.enter="handleQuery"
            />
          </el-form-item> -->
          <el-form-item label="状态" prop="status">
            <el-select v-model="queryParams.status" placeholder="请选择状态" clearable style="width: 150px">
              <el-option
                v-for="dict in statusOptions"
                :key="dict.dictValue"
                :label="dict.dictLabel"
                :value="dict.dictValue"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['search:room:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['search:room:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['search:room:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['search:room:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 数据表格区域 -->
    <div class="table-section">
      <el-card class="table-card">
        <template #header>
          <div class="table-header">
            <span><el-icon><List /></el-icon> 会议室列表</span>
            <div class="table-stats">
              <el-tag type="info" size="small">共 {{ total }} 条记录</el-tag>
            </div>
          </div>
        </template>

        <!-- 空状态 -->
        <div v-if="!loading && roomList.length === 0" class="empty-state">
          <el-empty description="暂无会议室记录">
            <el-button type="primary" @click="handleAdd">立即添加</el-button>
          </el-empty>
        </div>

        <!-- 数据表格 -->
        <el-table
          v-else
          v-loading="loading"
          :data="roomList"
          @selection-change="handleSelectionChange"
          class="room-table"
          stripe
          border
          :header-cell-style="{ background: '#f8fafc', color: '#606266', fontWeight: '600' }"
        >
          <el-table-column type="selection" width="55" align="center" />

          <el-table-column label="会议室ID" align="center" prop="roomId" width="100">
            <template #default="scope">
              <el-tag type="info" size="small">#{{ scope.row.roomId }}</el-tag>
            </template>
          </el-table-column>

          <el-table-column label="会议室名称" align="center" prop="roomName" min-width="150">
            <template #default="scope">
              <div class="room-info">
                <el-icon class="room-icon"><OfficeBuilding /></el-icon>
                <span class="room-name">{{ scope.row.roomName }}</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="容纳人数" align="center" prop="capacity" width="120">
            <template #default="scope">
              <div class="capacity-info">
                <el-icon class="capacity-icon"><User /></el-icon>
                <span class="capacity-text">{{ scope.row.capacity }}人</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="会议室图片" align="center" prop="imageUrl" width="120">
            <template #default="scope">
              <div class="image-wrapper">
                <image-preview :src="scope.row.imageUrl" :width="60" :height="60"/>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="状态" align="center" prop="status" width="120">
            <template #default="scope">
              <el-tag
                :type="statusTagType(scope.row.status)"
                class="status-tag"
                effect="dark"
              >
                {{ formatStatus(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="更新时间" align="center" prop="updateTime" width="180">
            <template #default="scope">
              <div class="time-info">
                <el-icon class="time-icon"><Clock /></el-icon>
                <span>{{ parseTime(scope.row.updateTime, '{y}-{m}-{d}') }}</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="备注" align="center" prop="remark" min-width="150">
            <template #default="scope">
              <div class="remark-info">
                <el-icon class="remark-icon"><Document /></el-icon>
                <span>{{ scope.row.remark || '无' }}</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="操作" align="center" width="200" class-name="small-padding fixed-width">
            <template #default="scope">
              <div class="action-buttons">
                <el-button
                  type="primary"
                  size="small"
                  icon="Edit"
                  @click="handleUpdate(scope.row)"
                  v-hasPermi="['search:room:edit']"
                  class="action-btn-small"
                >修改</el-button>
                <el-button
                  type="danger"
                  size="small"
                  icon="Delete"
                  @click="handleDelete(scope.row)"
                  v-hasPermi="['search:room:remove']"
                  class="action-btn-small"
                >删除</el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页组件 -->
        <div class="pagination-wrapper" v-show="total > 0">
          <pagination
            :total="total"
            v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize"
            @pagination="getList"
          />
        </div>
      </el-card>
    </div>

    <!-- 添加或修改会议室信息对话框 -->
    <el-dialog
      :title="title"
      v-model="open"
      width="700px"
      append-to-body
      class="room-dialog"
      :close-on-click-modal="false"
    >
      <template #header>
        <div class="dialog-header">
          <el-icon><EditPen /></el-icon>
          <span>{{ title }}</span>
        </div>
      </template>

      <el-form ref="roomRef" :model="form" :rules="rules" label-width="100px" class="room-form">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="会议室名称" prop="roomName">
              <el-input
                v-model="form.roomName"
                placeholder="请输入会议室名称"
                :prefix-icon="OfficeBuilding"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="容纳人数" prop="capacity">
              <el-input-number
                v-model="form.capacity"
                placeholder="请输入容纳人数"
                :min="1"
                :max="1000"
                controls-position="right"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="会议室状态" prop="status">
              <el-select v-model="form.status" placeholder="请选择状态" style="width: 100%">
                <el-option
                  v-for="option in statusOptions"
                  :key="option.dictValue"
                  :label="option.dictLabel"
                  :value="parseInt(option.dictValue)"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="会议室图片" prop="imageUrl">
              <image-upload v-model="form.imageUrl"/>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input
                v-model="form.remark"
                type="textarea"
                placeholder="请输入备注信息（可选）"
                :rows="3"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel" size="large">取 消</el-button>
          <el-button type="primary" @click="submitForm" size="large">
            <el-icon><Check /></el-icon>
            确 定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Room">
import { ref, reactive, toRefs, getCurrentInstance } from "vue";
import { listRoom, getRoom, delRoom, addRoom, updateRoom } from "@/api/search/room"
import {
  OfficeBuilding,
  Search,
  List,
  User,
  Document,
  Clock,
  EditPen,
  Check
} from '@element-plus/icons-vue';

const { proxy } = getCurrentInstance()

// 状态选项
const statusOptions = [
  { dictLabel: '停用', dictValue: '0' },
  { dictLabel: '启用', dictValue: '1' },
  { dictLabel: '维护中', dictValue: '2' }
]

const roomList = ref([])
const roomOptions = ref([])
const open = ref(false) 
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref("")

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    roomName: null,
    roomId: null,
    capacity: null,
    status: null,
  },
  rules: {
    roomName: [
      { required: true, message: "会议室名称不能为空", trigger: "blur" }
    ],
    capacity: [
      { required: true, message: "容纳人数不能为空", trigger: "blur" }
    ],
  }
})

const { queryParams, form, rules } = toRefs(data)

/** 查询会议室信息查询列表 */
function getList() {
  loading.value = true
  console.log('📤 发送到后端的查询参数:', queryParams.value)

  // 构建不包含capacity和roomId的查询参数
  const backendParams = { ...queryParams.value }
  const frontendCapacityFilter = backendParams.capacity
  const frontendRoomIdFilter = backendParams.roomId  // 保存roomId用于前端筛选
  delete backendParams.capacity
  delete backendParams.roomId  // 如果后端不支持roomId筛选，则删除

  // 如果有容纳人数过滤或会议室筛选，需要获取所有数据进行前端过滤
  if ((frontendCapacityFilter && frontendCapacityFilter > 0) || frontendRoomIdFilter) {
    backendParams.pageNum = 1
    backendParams.pageSize = 1000  // 获取足够多的数据
  }

  listRoom(backendParams).then(response => {
    console.log('📥 获取的 response：', response)
    let filteredRows = response.rows || []

    // 前端实现容纳人数过滤
    if (frontendCapacityFilter && frontendCapacityFilter > 0) {
      filteredRows = filteredRows.filter(room => {
        const roomCapacity = parseInt(room.capacity) || 0
        return roomCapacity >= frontendCapacityFilter
      })
      console.log(`🔍 容纳人数过滤: 要求>=${frontendCapacityFilter}人，过滤后${filteredRows.length}条记录`)
    }

    roomOptions.value = filteredRows
    
    // 前端实现会议室ID过滤
    if (frontendRoomIdFilter) {
      filteredRows = filteredRows.filter(room => room.roomId === frontendRoomIdFilter)
      console.log(`🔍 会议室过滤: 会议室ID=${frontendRoomIdFilter}，过滤后${filteredRows.length}条记录`)
    }

    // 前端分页处理
    // const pageSize = queryParams.value.pageSize
    // const pageNum = queryParams.value.pageNum
    // const startIndex = (pageNum - 1) * pageSize
    // const endIndex = startIndex + pageSize
    // roomList.value = filteredRows.slice(startIndex, endIndex)
  
    roomList.value = filteredRows // 直接展示后端分页返回的数据
    total.value = response.total || filteredRows.length // 保留总条数供分页控件使用
    
    loading.value = false
  }).catch(error => {
    console.error('❌ 查询失败:', error)
    loading.value = false
  })
}

/** 状态格式化 */
function formatStatus(status) {
  const map = { 0: '停用', 1: '启用', 2: '维护中'}
  return map[status] || '未知'
}

/** 状态标签类型 */
function statusTagType(status) {
  const map = { 0: 'info', 1: 'success', 2: 'warning'}
  return map[status]
}

// 取消按钮
function cancel() {
  open.value = false
  reset()
}

// 表单重置
function reset() {
  form.value = {
    roomId: null,
    roomName: null,
    capacity: null,
    imageUrl: null,
    status: null,
    createBy: null,
    createTime: null,
    updateBy: null,
    updateTime: null,
    remark: null
  }
  proxy.resetForm("roomRef")
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  console.log('🔍 搜索参数:', queryParams.value)
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  handleQuery()
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.roomId)
  single.value = selection.length != 1
  multiple.value = !selection.length
}

/** 新增按钮操作 */
function handleAdd() {
  reset()
  open.value = true
  title.value = "添加会议室信息查询"
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset()
  const _roomId = row.roomId || ids.value
  getRoom(_roomId).then(response => {
    form.value = response.data
    open.value = true
    title.value = "修改会议室信息查询"
  })
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["roomRef"].validate(valid => {
    if (valid) {
      if (form.value.roomId != null) {
        updateRoom(form.value).then(() => {
          proxy.$modal.msgSuccess("修改成功")
          open.value = false
          getList()
        })
      } else {
        addRoom(form.value).then(() => {
          proxy.$modal.msgSuccess("新增成功")
          open.value = false
          getList()
        })
      }
    }
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _roomIds = row.roomId || ids.value
  proxy.$modal.confirm('是否确认删除会议室信息查询编号为"' + _roomIds + '"的数据项？').then(function() {
    return delRoom(_roomIds)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('search/room/export', {
    ...queryParams.value
  }, `room_${new Date().getTime()}.xlsx`)
}

getList()
</script>

<style scoped lang="scss">
.room-management {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 84px);
}

/* 页面标题区域 */
.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  padding: 32px 40px;
  margin-bottom: 24px;
  color: white;
  position: relative;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
}

.page-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='4'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E") repeat;
  pointer-events: none;
}

.header-content {
  position: relative;
  z-index: 1;
}

.page-title {
  display: flex;
  align-items: center;
  font-size: 28px;
  font-weight: 700;
  margin: 0 0 12px 0;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);

  .el-icon {
    margin-right: 12px;
    font-size: 32px;
  }
}

.page-description {
  font-size: 16px;
  margin: 0;
  opacity: 0.9;
  line-height: 1.5;
}

/* 搜索区域 */
.search-section {
  margin-bottom: 20px;
}

.search-card {
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: none;
  overflow: hidden;
}

.search-header {
  display: flex;
  align-items: center;
  font-weight: 600;
  color: #303133;

  .el-icon {
    margin-right: 8px;
    color: #409eff;
  }
}

.search-form {
  padding: 8px 0;

  .el-form-item {
    margin-bottom: 16px;
  }
}

/* 表格区域 */
.table-section {
  margin-bottom: 20px;
}

.table-card {
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: none;
  overflow: hidden;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: #303133;

  .el-icon {
    margin-right: 8px;
    color: #409eff;
  }
}

.table-stats {
  display: flex;
  gap: 8px;
}

/* 空状态 */
.empty-state {
  padding: 60px 20px;
  text-align: center;
}

/* 表格样式 */
.room-table {
  border-radius: 8px;
  overflow: hidden;

  :deep(.el-table__header) {
    background: linear-gradient(180deg, #f8fafc 0%, #f1f5f9 100%);
  }

  :deep(.el-table__row) {
    transition: all 0.3s ease;

    &:hover {
      background-color: #f0f9ff !important;
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    }
  }

  :deep(.el-table__cell) {
    border-color: #f1f5f9;
    padding: 16px 12px;
  }
}

.room-info {
  display: flex;
  align-items: center;
  justify-content: center;

  .room-icon {
    margin-right: 8px;
    color: #409eff;
    font-size: 16px;
  }

  .room-name {
    font-weight: 500;
    color: #303133;
  }
}

.capacity-info {
  display: flex;
  align-items: center;
  justify-content: center;

  .capacity-icon {
    margin-right: 8px;
    color: #67c23a;
    font-size: 16px;
  }

  .capacity-text {
    font-weight: 500;
    color: #303133;
  }
}

.image-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
}

.time-info {
  display: flex;
  align-items: center;
  justify-content: center;

  .time-icon {
    margin-right: 8px;
    color: #e6a23c;
    font-size: 16px;
  }

  span {
    font-weight: 500;
    color: #606266;
    font-size: 13px;
  }
}

.remark-info {
  display: flex;
  align-items: center;
  justify-content: center;

  .remark-icon {
    margin-right: 8px;
    color: #909399;
    font-size: 16px;
  }

  span {
    font-weight: 500;
    color: #606266;
  }
}

.status-tag {
  font-weight: 600;
  border-radius: 6px;
  padding: 4px 12px;
}

.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
  flex-wrap: wrap;
}

.action-btn-small {
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }
}

/* 分页样式 */
.pagination-wrapper {
  margin-top: 24px;
  padding: 16px 0;
  border-top: 1px solid #f1f5f9;
  display: flex;
  justify-content: center;
}

/* 对话框样式 */
.room-dialog {
  :deep(.el-dialog) {
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  }

  :deep(.el-dialog__header) {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 24px;
    margin: 0;
  }

  :deep(.el-dialog__body) {
    padding: 32px 24px;
    background: #fafbfc;
  }

  :deep(.el-dialog__footer) {
    background: white;
    padding: 20px 24px;
    border-top: 1px solid #f1f5f9;
  }
}

.dialog-header {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: 600;

  .el-icon {
    margin-right: 12px;
    font-size: 20px;
  }
}

.room-form {
  .el-form-item {
    margin-bottom: 20px;

    :deep(.el-form-item__label) {
      font-weight: 600;
      color: #303133;
    }

    :deep(.el-input__wrapper) {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }

      &.is-focus {
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
      }
    }

    :deep(.el-textarea__inner) {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }

      &:focus {
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
      }
    }

    :deep(.el-input-number) {
      width: 100%;

      .el-input__wrapper {
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;

        &:hover {
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        &.is-focus {
          box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
        }
      }
    }

    :deep(.el-select) {
      width: 100%;

      .el-input__wrapper {
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;

        &:hover {
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        &.is-focus {
          box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;

  .el-button {
    border-radius: 8px;
    font-weight: 500;
    padding: 12px 24px;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    }
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .room-management {
    padding: 16px;
  }

  .page-header {
    padding: 24px 32px;
  }

  .page-title {
    font-size: 24px;

    .el-icon {
      font-size: 28px;
    }
  }
}

@media (max-width: 768px) {
  .room-management {
    padding: 12px;
  }

  .page-header {
    padding: 20px 24px;
  }

  .page-title {
    font-size: 20px;
    flex-direction: column;
    text-align: center;

    .el-icon {
      font-size: 24px;
      margin-right: 0;
      margin-bottom: 8px;
    }
  }

  .page-description {
    font-size: 14px;
    text-align: center;
  }

  .search-form {
    :deep(.el-form-item) {
      display: block;
      margin-right: 0;
      margin-bottom: 16px;

      .el-form-item__content {
        margin-left: 0 !important;
      }
    }
  }

  .action-buttons {
    flex-direction: column;
    gap: 8px;

    .action-btn-small {
      width: 100%;
    }
  }

  .room-dialog {
    :deep(.el-dialog) {
      width: 95% !important;
      margin: 5vh auto;
    }

    :deep(.el-dialog__body) {
      padding: 20px 16px;
    }
  }

  .room-form {
    .el-row {
      .el-col {
        width: 100%;
      }
    }
  }

  .dialog-footer {
    flex-direction: column;

    .el-button {
      width: 100%;
      margin: 0;
    }
  }
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.search-section,
.table-section {
  animation: fadeInUp 0.6s ease-out;
}

.search-section {
  animation-delay: 0.1s;
}

.table-section {
  animation-delay: 0.2s;
}

/* 自定义滚动条 */
:deep(.el-table__body-wrapper::-webkit-scrollbar) {
  width: 6px;
  height: 6px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-track) {
  background: #f1f5f9;
  border-radius: 3px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) {
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 3px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb:hover) {
  background: linear-gradient(135deg, #5a67d8, #6b46c1);
}
</style>
