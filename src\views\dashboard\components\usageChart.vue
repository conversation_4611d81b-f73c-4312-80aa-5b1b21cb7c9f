<template>
  <div class="usage-chart-container">
    <div class="chart-header">
      <h3 class="chart-title">可预约会议室</h3>
    </div>
    <div class="chart-content">
      <div ref="chartRef" class="chart" style="height: 300px;"></div>
    </div>
  </div>

</template>

<script setup>
import { ref, onMounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  roomData: {
    type: Array,
    default: () => []
  }
})

const chartRef = ref(null)
let chartInstance = null

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return
  
  chartInstance = echarts.init(chartRef.value)
  
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      data: props.roomData.map(item => item.name)
    },
    series: [
      {
        name: '会议室状态',
        type: 'pie',
        radius: '50%',
        center: ['60%', '50%'],
        data: props.roomData,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        },
        itemStyle: {
          color: function(params) {
            const colors = ['#409EFF', '#67C23A']
            return colors[params.dataIndex]
          }
        }
      }
    ]
  }
  
  chartInstance.setOption(option)
}

// 更新图表数据
const updateChart = () => {
  if (chartInstance && props.roomData.length > 0) {
    chartInstance.setOption({
      series: [{
        data: props.roomData
      }]
    })
  }
}

// 监听数据变化
watch(() => props.roomData, (newData) => {
  if (newData && newData.length > 0) {
    nextTick(() => {
      initChart()
    })
  }
}, { deep: true, immediate: true })

onMounted(() => {
  nextTick(() => {
    initChart()
  })
})
</script>

<style lang="scss" scoped>
.usage-chart-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  height: 400px;
  
  .chart-header {
    margin-bottom: 20px;
    
    .chart-title {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }
  }
  
  .chart-content {
    height: calc(100% - 60px);
    
    .chart {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
