<template>
    <div class="dashboard-container">
      <!-- 页面头部 -->
      <div class="dashboard-header">
        <div class="header-left">
          <h2 class="page-title">数据总览</h2>
        </div>
      </div>

      <!-- 统计卡片区域 -->
      <StatisticsCards :statistics="statistics" :loading="loading" />

      <!-- 图表区域 -->
      <div class="charts-section">
        <el-row :gutter="20">
          <el-col :span="16">
            <div class="dashboard-card trend-card">
              <TrendChart 
                :reservation-data="reservationList" 
                :loading="loading"
              />
            </div>
          </el-col>
          <el-col :xs="24" :sm="24" :md="8" :lg="8" :xl="8">
            <UsageChart :room-data="roomUsageData" />
          </el-col>
        </el-row>
      </div>

      <!-- 最近预约列表 -->
      <RecentList 
        :reservation-data="reservationList" 
        :room-data="roomList"
        :loading="loading"
      />
    </div>
  </template>

  <script setup>
  import { ref, reactive, onMounted, computed } from 'vue'
  import { ElMessage } from 'element-plus'

  //引入API
  import { listUser, getUserProfile } from '@/api/system/user'
  import { listRoom } from '@/api/search/room'
  import { listReservation } from '@/api/reserve/reservation'

  // 导入子组件
  import StatisticsCards from './components/statisticsCards.vue'
  import TrendChart from './components/trendChart.vue'
  import UsageChart from './components/usageChart.vue'
  import RecentList from './components/recentList.vue'

  // 响应式数据
  const loading = ref(false)
  const statistics = reactive({
    todayReservations: 0,      // 今日预约数
    pendingReservations: 0,    // 待审批预约数
    availableRooms: 0,         // 启用的会议室
    totalUsers: 0,             // 总用户数
    activeUsers: 0,            // 活跃用户数
    totalRooms: 0,             // 总会议室数
    unavailableRooms: 0,       // 停用+维护中的会议室
    totalReservations: 0       // 总预约数
  })

  const userInfo = ref({})
  const roomList = ref([])
  const reservationList = ref([])  

  // 计算会议室使用率数据
  const roomUsageData = computed(() => {
    if (statistics.totalRooms === 0) return []
    
    return [
      { name: '可预约', value: statistics.availableRooms },      // 启用状态
      { name: '不可用', value: statistics.unavailableRooms }     // 停用+维护中
    ]
  })

  //加载用户统计数据
  const loadUserStats = async () => {
    try {
      const response = await listUser({ pageNum: 1, pageSize: 9999 })
      const users = response.rows || []
      
      statistics.totalUsers = users.length
      statistics.activeUsers = users.filter(user => user.status === '0').length
      
    } catch (error) {
      console.error('加载用户数据失败:', error)
      ElMessage.error('加载用户数据失败')
    }
  }

  //加载会议室统计数据
  const loadRoomStats = async () => {
    try {
      const response = await listRoom({ pageNum: 1, pageSize: 9999 })
      const rooms = response.rows || []
      roomList.value = rooms
      
      //调试信息
      console.log('=== 会议室数据调试 ===')
      console.log('总数量:', rooms.length)
      if (rooms.length > 0) {
        console.log('第一间会议室:', rooms[0])
        console.log('状态分布:', {
          启用: rooms.filter(room => room.status === 1).length,
          停用: rooms.filter(room => room.status === 0).length,
          维护中: rooms.filter(room => room.status === 2).length
        })
      }
      
      statistics.totalRooms = rooms.length
      
      //会议室状态
      statistics.availableRooms = rooms.filter(room => room.status === 1).length        
      statistics.unavailableRooms = rooms.filter(room => room.status === 0 || room.status === 2).length 
      
      console.log('统计结果:', {
        总计: statistics.totalRooms,
        可用: statistics.availableRooms,
        不可用: statistics.unavailableRooms
      })
      
    } catch (error) {
      console.error('加载会议室数据失败:', error)
      ElMessage.error('加载会议室数据失败')
    }
  }

  //加载当前用户信息
  const loadCurrentUser = async () => {
    try {
      const response = await getUserProfile()
      userInfo.value = response.data
    } catch (error) {
      console.error('加载用户信息失败:', error)
    }
  }

  // 加载预约统计数据
  const loadReservationStats = async () => {
    try {
      const response = await listReservation({ pageNum: 1, pageSize: 9999 })
      const reservations = response.rows || []
      reservationList.value = reservations
      
      statistics.totalReservations = reservations.length
      
      //今日预约数
      const today = new Date().toISOString().split('T')[0] // 2025-08-01
      statistics.todayReservations = reservations.filter(reservation => {
        const startTime = reservation.startTime
        return startTime && startTime.startsWith(today)
      }).length
      
      //待审核预约数
      statistics.pendingReservations = reservations.filter(reservation => {
        return reservation.status === 1  
      }).length

      //预约状态分析
      console.log('=== 预约状态分析 ===')
      console.log('状态统计:', (() => {
        const statusCount = {}
        reservations.forEach(reservation => {
          const status = reservation.status
          statusCount[status] = (statusCount[status] || 0) + 1
        })
        return statusCount
      })())
      
      console.log('预约统计完成:', {
        总预约: statistics.totalReservations,
        今日预约: statistics.todayReservations,
        待审核: statistics.pendingReservations
      })
      
    } catch (error) {
      console.error('加载预约数据失败:', error)
      ElMessage.error('加载预约数据失败')
    }
  }

  // 刷新所有数据
  const refreshData = async () => {
    loading.value = true
    try {
      await Promise.all([
        loadUserStats(),
        loadRoomStats(), 
        loadCurrentUser(),
        loadReservationStats()  
      ])
      // ElMessage.success('数据刷新成功')
    } catch (error) {
      ElMessage.error('数据刷新失败')
    } finally {
      loading.value = false
    }
  }

  // 生命周期
  onMounted(() => {
    console.log('Dashboard 组件已挂载')
    refreshData()
  })
  </script>

  <style lang="scss" scoped>
  .dashboard-container {
    padding: 20px;
    
    .dashboard-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      
      .page-title {
        margin: 0;
        font-size: 22px;
        font-weight: 600;
      }
      
      .page-subtitle {
        color: #909399;
        font-size: 14px;
        margin-top: 5px;
        
        .user-welcome {
          color: #409EFF;
          font-weight: 500;
        }
      }
      
      .header-right {
        display: flex;
        align-items: center;
        gap: 15px;
        
        .date-filter-placeholder {
          width: 240px;
          height: 32px;
          background: #f5f7fa;
          border-radius: 4px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #909399;
        }
      }
    }
    
    .charts-section {
      margin-bottom: 20px;
    }
  }
  .trend-card {
    height: 400px;
    padding: 0; 
  }
  </style>